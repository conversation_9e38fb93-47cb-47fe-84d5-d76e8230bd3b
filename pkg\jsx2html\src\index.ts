import express from "express";
import cors from "cors";
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { errorHandler } from './middlewares/error.middleware';
import { ConnectRouter } from "@connectrpc/connect";
import { Jsx2HtmlServiceImpl } from "./services/jsx2html";
import { Jsx2HtmlService } from "./proto/jsx2html/v1/service_pb.js";
import { connectNodeAdapter, ConnectNodeAdapterOptions } from "@connectrpc/connect-node";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config({ path: path.resolve(__dirname, '../.env') });

function main() {
    const app = express();
    const PORT = process.env.PORT || 3002;

    app.use(cors({
        origin: "*",
        methods: ['GET', 'POST', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Connect-Protocol-Version']
    }));

    app.use(express.json({ limit: '50mb' }));
    app.use(express.urlencoded({ limit: '50mb', extended: true }));

    const routes = (router: ConnectRouter) => router.service(
        Jsx2HtmlService,
        Jsx2HtmlServiceImpl
    );
    const connectNodeOptions: ConnectNodeAdapterOptions = { routes };

    app.use('/', connectNodeAdapter(connectNodeOptions));

    app.get('/health', (req, res) => {
        res.json({ status: 'ok', service: 'jsx2html-service' });
    });

    app.use(errorHandler);

    app.listen(PORT, () => {
        console.log(`Jsx2Html service listening on port ${PORT}`);
    });
}

main();

process.on('SIGINT', () => {
    console.log('SIGINT signal received: closing HTTP server');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('SIGTERM signal received: closing HTTP server');
    process.exit(0);
});
import { ServiceImpl } from "@connectrpc/connect";
import { Jsx2HtmlService } from "../proto/jsx2html/v1/service_pb.js";
import { GenerateRequest, GenerateResponse, GenerateResponseSchema, ReportMetadata, ReportSection } from "../proto/jsx2html/v1/model_pb.js";
import { ReportDocument, ReportDocumentProps } from '../components/ReportDocument';
import { renderToStaticMarkup } from 'react-dom/server';
import { create, Message } from "@bufbuild/protobuf";

export const Jsx2HtmlServiceImpl: ServiceImpl<typeof Jsx2HtmlService> = {
    async generate(req: Message<GenerateRequest>): Promise<Message<GenerateResponse>> {
        if (!req.sections || !Array.isArray(req.sections) || req.sections.length === 0) {
            throw new Error("Invalid sections provided in the request.");
        }
        if (!req.metadata) {
            throw new Error("Metadata is required in the request.");
        }

        const props: ReportDocumentProps = {
            sections: req.sections as ReportSection[],
            metadata: req.metadata as ReportMetadata,
        };

        const html = renderToStaticMarkup(
            ReportDocument(props)
        );

        return create(GenerateResponseSchema, {
            html: html,
        });
    },
};
